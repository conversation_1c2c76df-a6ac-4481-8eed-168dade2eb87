# coding:utf-8
"""
测试SSL证书错误绕过功能
用于验证 handle_ssl_certificate_error 方法是否正常工作
"""

import logging
import time
from wechat_browser_automation import WeChatBrowserAutomation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ssl_bypass_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_ssl_bypass():
    """测试SSL证书错误绕过功能"""
    print("=== SSL证书错误绕过功能测试 ===")
    
    automation = WeChatBrowserAutomation()
    
    # 测试用的微信文章链接（可能会遇到SSL证书错误）
    test_url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect"
    
    print(f"\n📋 测试链接: {test_url}")
    print("\n--- 步骤 1: 发送链接到文件传输助手 ---")
    
    # 发送链接到文件传输助手
    if not automation.send_link_to_file_transfer(test_url):
        print("❌ 发送链接失败")
        return False
    
    print("✅ 链接已发送")
    
    print("\n--- 步骤 2: 点击链接打开页面 ---")
    time.sleep(2)
    
    try:
        # 查找并点击最新消息
        message_list_control = automation.wechat_window.ListControl(Name='消息')
        if not message_list_control.Exists(3):
            print("❌ 未能找到聊天记录列表控件")
            return False

        message_items = message_list_control.GetChildren()
        if not message_items:
            print("❌ 聊天记录为空")
            return False
        
        # 点击最新消息
        latest_message_item = message_items[-1]
        latest_message_item.Click(simulateMove=True)
        print("✅ 已点击最新链接")
        
        # 等待页面加载
        print("\n--- 步骤 3: 等待页面加载并检测SSL证书错误 ---")
        time.sleep(4)
        
        # 检测并处理SSL证书错误
        print("🔍 开始检测SSL证书错误页面...")
        ssl_error_detected = automation.handle_ssl_certificate_error()
        
        if ssl_error_detected:
            print("✅ 检测到SSL证书错误页面，已自动使用 'thisisunsafe' 绕过")
            print("⏳ 等待页面重新加载...")
            time.sleep(3)
            
            # 可选：再次检测确认绕过成功
            print("🔍 验证绕过结果...")
            still_has_error = automation.handle_ssl_certificate_error()
            if not still_has_error:
                print("✅ SSL证书错误已成功绕过，页面正常加载")
            else:
                print("⚠️ SSL证书错误仍然存在，可能需要手动处理")
        else:
            print("ℹ️ 未检测到SSL证书错误页面，页面正常加载")
        
        print("\n--- 步骤 4: 可选的页面刷新测试 ---")
        print("是否要测试自动刷新功能？(y/n): ", end="")
        user_input = input().strip().lower()
        
        if user_input == 'y':
            print("🔄 开始执行自动刷新测试...")
            refresh_success = automation.auto_refresh_browser(refresh_count=2, refresh_delay=3.0)
            if refresh_success:
                print("✅ 自动刷新测试成功")
            else:
                print("❌ 自动刷新测试失败")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logging.error(f"测试过程中发生错误: {e}")
        return False

def test_ssl_detection_only():
    """仅测试SSL证书错误检测功能（不发送链接）"""
    print("=== 仅测试SSL证书错误检测功能 ===")
    print("请手动打开一个可能有SSL证书错误的页面，然后按回车继续...")
    input()
    
    automation = WeChatBrowserAutomation()
    
    print("🔍 开始检测当前页面是否有SSL证书错误...")
    ssl_error_detected = automation.handle_ssl_certificate_error()
    
    if ssl_error_detected:
        print("✅ 检测到SSL证书错误页面，已自动处理")
    else:
        print("ℹ️ 未检测到SSL证书错误页面")
    
    print("=== 检测完成 ===")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整测试（发送链接 + SSL检测 + 可选刷新）")
    print("2. 仅测试SSL检测功能")
    print("请输入选择 (1/2): ", end="")
    
    choice = input().strip()
    
    if choice == "1":
        test_ssl_bypass()
    elif choice == "2":
        test_ssl_detection_only()
    else:
        print("无效选择，退出程序")
