2025-08-04 15:35:14,233 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:35:14,233 - INFO - ================================================================================
2025-08-04 15:35:14,233 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:35:14,233 - INFO - ================================================================================
2025-08-04 15:35:14,233 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:35:14,305 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:35:14,306 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:35:14,306 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:35:14,307 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:35:14,307 - INFO - ============================================================
2025-08-04 15:35:14,307 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:35:14,307 - INFO - ============================================================
2025-08-04 15:35:14,307 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:35:14,307 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:35:14,307 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:35:14,307 - INFO - === 开始重置网络状态 ===
2025-08-04 15:35:14,307 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:35:14,393 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:35:14,394 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:35:14,394 - INFO - 系统代理已成功关闭
2025-08-04 15:35:14,394 - INFO - ✅ 代理关闭操作
2025-08-04 15:35:14,394 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:35:15,330 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:35:15,330 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:35:15,330 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:35:15,330 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:35:15,330 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:35:15,828 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:35:15,829 - INFO - 🔄 Cookie抓取器进程已启动，PID: 30904
2025-08-04 15:35:15,829 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:35:18,829 - INFO - 等待代理服务启动...
2025-08-04 15:35:19,311 - INFO - 代理服务已启动并正常工作
2025-08-04 15:35:19,312 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 30904)
2025-08-04 15:35:19,312 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:35:19,312 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:35:19,313 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:35:19,313 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:35:19,313 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:35:19,314 - INFO - 正在查找微信主窗口...
2025-08-04 15:35:19,380 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:35:19,380 - INFO - 正在激活微信窗口...
2025-08-04 15:35:21,890 - INFO - 微信窗口已激活。
2025-08-04 15:35:21,891 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:35:28,601 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:35:28,602 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:35:31,934 - INFO - 正在查找聊天输入框...
2025-08-04 15:35:33,935 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:35:33,947 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:35:33,948 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:35:35,526 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:35:37,814 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:35:37,990 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:35:38,800 - INFO - 链接已发送。
2025-08-04 15:35:41,801 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:35:43,893 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:35:44,618 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:35:47,619 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:35:47,619 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:35:47,620 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:35:47,633 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:35:50,340 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:35:51,841 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:35:59,969 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:35:59,969 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:35:59,969 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:35:59,969 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:35:59,969 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:35:59,973 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:02,681 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:03,182 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:03,183 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:03,192 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:05,900 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:07,401 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:16,059 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:16,059 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:18,560 - INFO - 第 1 次刷新完成
2025-08-04 15:36:20,061 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:36:20,062 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:20,062 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:20,070 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:22,775 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:23,275 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:23,276 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:23,283 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:25,990 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:27,491 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:36,139 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:36,139 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:38,640 - INFO - 第 2 次刷新完成
2025-08-04 15:36:40,141 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:36:40,142 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:40,143 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:40,154 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:42,859 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:43,360 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:43,360 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:43,365 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:46,073 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:47,573 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:56,240 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:56,241 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:58,741 - INFO - 第 3 次刷新完成
2025-08-04 15:36:58,742 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:36:58,744 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:36:58,744 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:36:58,745 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:36:59,746 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:36:59,748 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:36:59,749 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:36:59,750 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:36:59,751 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:36:59,751 - INFO - 正在停止Cookie抓取器 (PID: 30904)...
2025-08-04 15:36:59,753 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:36:59,753 - INFO - 正在验证并清理代理设置...
2025-08-04 15:36:59,753 - INFO - === 开始重置网络状态 ===
2025-08-04 15:36:59,753 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:36:59,885 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:36:59,885 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:36:59,885 - INFO - 系统代理已成功关闭
2025-08-04 15:36:59,885 - INFO - ✅ 代理关闭操作
2025-08-04 15:36:59,885 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:37:01,549 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:37:01,550 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:37:01,550 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:37:03,494 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:37:03,495 - INFO - ✅ 网络连接验证正常
2025-08-04 15:37:06,496 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:37:06,497 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-04 15:39:18,249 - INFO - ✅ 公众号 '南京党建' 爬取完成！获取 15 篇文章
2025-08-04 15:39:18,250 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京党建_20250804_153918.xlsx
2025-08-04 15:39:18,250 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 15:39:33,250 - INFO - ============================================================
2025-08-04 15:39:33,251 - INFO - 📍 处理第 2/2 个公众号: 南京警方
2025-08-04 15:39:33,251 - INFO - ============================================================
2025-08-04 15:39:33,251 - INFO - [步骤 1/5] 为 '南京警方' 启动 Cookie 抓取器...
2025-08-04 15:39:33,252 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:39:33,252 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:39:33,252 - INFO - === 开始重置网络状态 ===
2025-08-04 15:39:33,253 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:39:33,348 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:39:33,349 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:39:33,349 - INFO - 系统代理已成功关闭
2025-08-04 15:39:33,349 - INFO - ✅ 代理关闭操作
2025-08-04 15:39:33,349 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:39:35,589 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:39:35,589 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:39:35,590 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:39:35,590 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:39:35,591 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:39:36,174 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:39:36,175 - INFO - 🔄 Cookie抓取器进程已启动，PID: 7264
2025-08-04 15:39:36,175 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:39:39,176 - INFO - 等待代理服务启动...
2025-08-04 15:39:40,436 - INFO - 代理服务已启动并正常工作
2025-08-04 15:39:40,437 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 7264)
2025-08-04 15:39:40,437 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:39:40,437 - INFO - [步骤 2/5] 为 '南京警方' 启动 UI 自动化...
2025-08-04 15:39:40,438 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:39:40,438 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:39:40,439 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:39:40,439 - INFO - 正在查找微信主窗口...
2025-08-04 15:39:40,604 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:39:40,604 - INFO - 正在激活微信窗口...
2025-08-04 15:39:43,118 - INFO - 微信窗口已激活。
2025-08-04 15:39:43,119 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:39:49,802 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:39:49,803 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:39:53,136 - INFO - 正在查找聊天输入框...
2025-08-04 15:39:55,137 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:39:55,145 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:39:55,145 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:39:56,711 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&mid=2247869690&idx=1&sn=11191456c1a9d5d930f1a385c2871b68&chksm=eaa0d2a4ddd75bb2309e8c8eac541bfbc8a2a4519ad5bd98fde6ad96e532cf17dc9fc7cb72a5#rd
2025-08-04 15:39:58,999 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:39:59,198 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:40:00,001 - INFO - 链接已发送。
2025-08-04 15:40:03,002 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:40:05,094 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:40:05,817 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:40:08,818 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:40:08,819 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:08,819 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:08,822 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:11,527 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:13,028 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:21,294 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:40:21,294 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:40:21,294 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:40:21,294 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:21,294 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:21,299 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:24,005 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:24,506 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:24,506 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:24,517 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:27,223 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:28,724 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:37,498 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:40:37,499 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:40:40,000 - INFO - 第 1 次刷新完成
2025-08-04 15:40:41,501 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:40:41,501 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:41,501 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:41,506 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:44,211 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:44,712 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:44,713 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:44,725 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:47,431 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:48,933 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:57,653 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:40:57,654 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:41:00,154 - INFO - 第 2 次刷新完成
2025-08-04 15:41:01,655 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:41:01,656 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:41:01,656 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:41:01,665 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:41:04,371 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:41:04,872 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:41:04,873 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:41:04,881 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:41:07,587 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:41:09,088 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:41:17,833 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:41:17,834 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:41:20,334 - INFO - 第 3 次刷新完成
2025-08-04 15:41:20,335 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:41:20,336 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:41:20,337 - INFO - [步骤 3/5] 等待 '南京警方' 的 Cookie 数据...
2025-08-04 15:41:20,337 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:41:21,338 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:41:21,339 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:41:21,340 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:41:21,340 - INFO - [步骤 4/5] 停止 '南京警方' 的 Cookie 抓取器...
2025-08-04 15:41:21,341 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:41:21,342 - INFO - 正在停止Cookie抓取器 (PID: 7264)...
2025-08-04 15:41:21,345 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:41:21,345 - INFO - 正在验证并清理代理设置...
2025-08-04 15:41:21,345 - INFO - === 开始重置网络状态 ===
2025-08-04 15:41:21,346 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:41:21,460 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:41:21,460 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:41:21,461 - INFO - 系统代理已成功关闭
2025-08-04 15:41:21,461 - INFO - ✅ 代理关闭操作
2025-08-04 15:41:21,461 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:41:24,050 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:41:24,051 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:41:24,051 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:41:26,042 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:41:26,042 - INFO - ✅ 网络连接验证正常
2025-08-04 15:41:29,043 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:41:29,044 - INFO - [步骤 5/5] 开始爬取 '南京警方' 的文章...
2025-08-04 15:41:29,166 - WARNING - ⚠️ 公众号 '南京警方' 未获取到任何文章数据
2025-08-04 15:41:29,166 - INFO - ================================================================================
2025-08-04 15:41:29,166 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 15:41:29,167 - INFO - ================================================================================
2025-08-04 15:41:29,167 - INFO - ✅ 成功处理: 1 个公众号
2025-08-04 15:41:29,167 - INFO - ❌ 失败处理: 1 个公众号
2025-08-04 15:41:29,167 - INFO - 📄 总计文章: 15 篇
2025-08-04 15:41:29,201 - INFO - 🎉 汇总数据已保存到:
2025-08-04 15:41:29,202 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_154129.xlsx
2025-08-04 15:41:29,202 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_154129.json
2025-08-04 15:41:29,202 - INFO - ================================================================================
2025-08-04 15:41:29,202 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 15:41:29,202 - INFO - ================================================================================
