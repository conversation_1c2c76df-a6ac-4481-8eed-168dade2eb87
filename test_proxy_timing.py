#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理设置时序
验证代理是否立即设置并可用
"""

import os
import time
import subprocess
import winreg
import threading

def check_proxy_status():
    """检查当前系统代理状态"""
    try:
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                           0, winreg.KEY_READ)
        proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
        try:
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
        except:
            proxy_server = ""
        winreg.CloseKey(key)
        
        return proxy_enable == 1, proxy_server
    except Exception as e:
        print(f"❌ 检查代理状态失败: {e}")
        return False, ""

def monitor_proxy_changes():
    """监控代理状态变化"""
    print("🔍 开始监控代理状态变化...")
    
    initial_enabled, initial_server = check_proxy_status()
    print(f"初始状态: {'启用' if initial_enabled else '禁用'} - {initial_server}")
    
    # 启动mitmproxy进程
    print("\n🚀 启动mitmproxy...")
    try:
        mitm_process = subprocess.Popen([
            "mitmdump", "-s", "cookie_extractor.py", 
            "--listen-port", "8080", "--ssl-insecure"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print(f"✅ mitmproxy已启动 (PID: {mitm_process.pid})")
        
        # 监控代理状态变化
        print("\n📊 监控代理状态变化 (前30秒):")
        for i in range(30):
            enabled, server = check_proxy_status()
            status = "启用" if enabled else "禁用"
            
            if enabled != initial_enabled or server != initial_server:
                print(f"⚡ 第{i+1}秒: 代理状态变化 - {status} - {server}")
                if enabled and "127.0.0.1:8080" in server:
                    print("✅ 代理已正确设置！")
                    break
            elif i % 5 == 0:  # 每5秒显示一次状态
                print(f"⏳ 第{i+1}秒: {status} - {server}")
            
            time.sleep(1)
        
        print(f"\n🎯 最终代理状态: {'启用' if enabled else '禁用'} - {server}")
        
        # 等待用户测试
        print("\n📝 现在可以在微信中刷新公众号文章进行测试")
        print("按 Enter 键停止测试...")
        input()
        
        # 停止mitmproxy
        print("🛑 停止mitmproxy...")
        mitm_process.terminate()
        try:
            mitm_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            mitm_process.kill()
            mitm_process.wait()
        
        # 检查最终状态
        final_enabled, final_server = check_proxy_status()
        print(f"清理后状态: {'启用' if final_enabled else '禁用'} - {final_server}")
        
    except FileNotFoundError:
        print("❌ 找不到mitmdump命令，请确保mitmproxy已安装")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

def main():
    """主函数"""
    print("🧪 代理设置时序测试")
    print("="*50)
    
    try:
        monitor_proxy_changes()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
