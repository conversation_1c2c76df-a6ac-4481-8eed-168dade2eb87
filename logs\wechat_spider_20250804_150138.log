2025-08-04 15:01:38,351 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:01:38,351 - INFO - ================================================================================
2025-08-04 15:01:38,351 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:01:38,351 - INFO - ================================================================================
2025-08-04 15:01:38,351 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:01:38,421 - INFO - 找到有效目标 1: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 15:01:38,422 - INFO - 找到有效目标 2: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 15:01:38,422 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:01:38,423 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:01:38,423 - INFO - ============================================================
2025-08-04 15:01:38,423 - INFO - 📍 处理第 1/2 个公众号: 金陵档案
2025-08-04 15:01:38,423 - INFO - ============================================================
2025-08-04 15:01:38,423 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 15:01:38,423 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:01:38,423 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:01:38,423 - INFO - === 开始重置网络状态 ===
2025-08-04 15:01:38,423 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:01:38,508 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:01:38,508 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:01:38,508 - INFO - 系统代理已成功关闭
2025-08-04 15:01:38,508 - INFO - ✅ 代理关闭操作
2025-08-04 15:01:38,509 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:01:39,446 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:01:39,446 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:01:39,446 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:01:39,446 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:01:39,446 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:01:39,936 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:01:39,937 - INFO - 🔄 Cookie抓取器进程已启动，PID: 29548
2025-08-04 15:01:39,937 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:01:42,938 - INFO - 等待代理服务启动...
2025-08-04 15:01:43,418 - INFO - 代理服务已启动并正常工作
2025-08-04 15:01:43,419 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 29548)
2025-08-04 15:01:43,419 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:01:43,420 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 15:01:43,420 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:43,420 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:01:43,421 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:01:43,421 - INFO - 正在查找微信主窗口...
2025-08-04 15:01:43,491 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:01:43,491 - INFO - 正在激活微信窗口...
2025-08-04 15:01:46,005 - INFO - 微信窗口已激活。
2025-08-04 15:01:46,006 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:01:53,034 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:01:53,035 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:01:56,368 - INFO - 正在查找聊天输入框...
2025-08-04 15:01:58,369 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:01:58,382 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:01:58,382 - INFO - 点击聊天输入区域坐标: (423, 717)
2025-08-04 15:01:59,992 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&mid=2247539053&idx=1&sn=9dcae8e371bf87efbe121cf3ed8afee7&chksm=cf1b71bdf86cf8abd355f4b297ae51941771cc98fc1eb481b875e64b6bee30ac39c4f06a4133#rd
2025-08-04 15:02:02,283 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:02:02,445 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:02:03,249 - INFO - 链接已发送。
2025-08-04 15:02:06,250 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:02:08,335 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:02:09,068 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:02:12,068 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:02:12,069 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:02:12,069 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:02:12,077 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:02:14,784 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:02:16,285 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:02:23,202 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:02:23,202 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:02:23,202 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:02:23,203 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:02:23,203 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:02:23,212 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
