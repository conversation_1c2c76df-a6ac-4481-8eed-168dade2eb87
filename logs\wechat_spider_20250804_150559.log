2025-08-04 15:05:59,589 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:05:59,590 - INFO - ================================================================================
2025-08-04 15:05:59,590 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:05:59,590 - INFO - ================================================================================
2025-08-04 15:05:59,590 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:05:59,660 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:05:59,660 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:05:59,661 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:05:59,661 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:05:59,661 - INFO - ============================================================
2025-08-04 15:05:59,661 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:05:59,661 - INFO - ============================================================
2025-08-04 15:05:59,661 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:05:59,662 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:05:59,662 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:05:59,662 - INFO - === 开始重置网络状态 ===
2025-08-04 15:05:59,662 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:05:59,750 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:05:59,751 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:05:59,751 - INFO - 系统代理已成功关闭
2025-08-04 15:05:59,751 - INFO - ✅ 代理关闭操作
2025-08-04 15:05:59,751 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:06:00,695 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:06:00,696 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:06:00,696 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:06:00,697 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:06:00,697 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:06:01,191 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:06:01,192 - INFO - 🔄 Cookie抓取器进程已启动，PID: 40100
2025-08-04 15:06:01,192 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:06:04,192 - INFO - 等待代理服务启动...
2025-08-04 15:06:04,814 - INFO - 代理服务已启动并正常工作
2025-08-04 15:06:04,815 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 40100)
2025-08-04 15:06:04,816 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:06:04,816 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:06:04,817 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:06:04,818 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:06:04,818 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:06:04,818 - INFO - 正在查找微信主窗口...
2025-08-04 15:06:04,885 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:06:04,886 - INFO - 正在激活微信窗口...
2025-08-04 15:06:07,399 - INFO - 微信窗口已激活。
2025-08-04 15:06:07,400 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:06:14,136 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:06:14,137 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:06:17,467 - INFO - 正在查找聊天输入框...
2025-08-04 15:06:19,468 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:06:19,479 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:06:19,480 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:06:21,084 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:06:23,371 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:06:23,538 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:06:24,349 - INFO - 链接已发送。
2025-08-04 15:06:27,351 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:06:29,432 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:06:30,168 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:06:33,169 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:06:33,169 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:33,170 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:33,179 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:35,886 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:37,388 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:06:45,558 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:06:45,558 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:06:45,558 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:06:45,558 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:45,558 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:45,562 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:48,268 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:48,769 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:48,769 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:48,780 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:51,485 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:52,986 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:01,619 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:01,620 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:04,120 - INFO - 第 1 次刷新完成
2025-08-04 15:07:05,621 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:07:05,622 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:05,622 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:05,634 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:08,340 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:08,841 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:08,842 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:08,854 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:11,559 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:13,060 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:21,708 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:21,709 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:24,210 - INFO - 第 2 次刷新完成
2025-08-04 15:07:25,711 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:07:25,712 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:25,713 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:25,724 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:28,431 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:28,932 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:28,933 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:28,942 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:31,648 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:33,150 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:41,789 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:41,790 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:44,290 - INFO - 第 3 次刷新完成
2025-08-04 15:07:44,291 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:07:44,292 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:07:44,292 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:07:44,292 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:08:29,311 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:08:29,313 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:08:29,313 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:08:29,314 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:08:29,314 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:08:29,315 - INFO - 正在停止Cookie抓取器 (PID: 40100)...
2025-08-04 15:08:29,316 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:08:29,317 - INFO - 正在验证并清理代理设置...
2025-08-04 15:08:29,317 - INFO - === 开始重置网络状态 ===
2025-08-04 15:08:29,317 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:08:29,415 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:08:29,416 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:08:29,416 - INFO - 系统代理已成功关闭
2025-08-04 15:08:29,416 - INFO - ✅ 代理关闭操作
2025-08-04 15:08:29,416 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:08:30,427 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:08:30,427 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:08:30,428 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:08:31,509 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:08:31,509 - INFO - ✅ 网络连接验证正常
2025-08-04 15:08:34,510 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:08:34,510 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
