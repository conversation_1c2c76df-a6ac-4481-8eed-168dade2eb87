#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Cookie抓取器
"""

import time
from read_cookie import ReadCookie
import subprocess

def check_mitmproxy_running():
    """检查mitmproxy是否在运行"""
    try:
        result = subprocess.run(['tasklist', '/fi', 'imagename eq mitmdump.exe'], 
                              capture_output=True, text=True, timeout=5)
        return 'mitmdump.exe' in result.stdout
    except:
        return False

def main():
    print("🧪 测试修复后的Cookie抓取器")
    print("="*50)
    
    cookie_reader = ReadCookie()
    
    print("\n1️⃣ 启动Cookie抓取器...")
    success = cookie_reader.start_cookie_extractor()
    
    if success:
        print("✅ Cookie抓取器启动成功")
        
        # 等待几秒钟，然后检查进程是否还在运行
        print("\n2️⃣ 等待5秒后检查进程状态...")
        time.sleep(5)
        
        if check_mitmproxy_running():
            print("✅ mitmproxy进程仍在运行")
            
            print("\n3️⃣ 现在可以在微信中刷新公众号文章进行测试")
            print("按 Enter 键停止测试...")
            input()
            
        else:
            print("❌ mitmproxy进程已退出")
        
        print("\n4️⃣ 停止Cookie抓取器...")
        cookie_reader.stop_cookie_extractor()
        print("✅ 测试完成")
        
    else:
        print("❌ Cookie抓取器启动失败")

if __name__ == "__main__":
    main()
