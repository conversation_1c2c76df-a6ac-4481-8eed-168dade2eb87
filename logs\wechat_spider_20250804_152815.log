2025-08-04 15:28:15,926 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:28:15,926 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:28:15,926 - INFO - ================================================================================
2025-08-04 15:28:15,926 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:28:15,926 - INFO - ================================================================================
2025-08-04 15:28:15,926 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:28:16,003 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:28:16,003 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:28:16,003 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:28:16,003 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:28:16,003 - INFO - ============================================================
2025-08-04 15:28:16,003 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:28:16,004 - INFO - ============================================================
2025-08-04 15:28:16,004 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:28:16,004 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:28:16,004 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:28:16,004 - INFO - === 开始重置网络状态 ===
2025-08-04 15:28:16,004 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:28:16,092 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:28:16,092 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:28:16,092 - INFO - 系统代理已成功关闭
2025-08-04 15:28:16,092 - INFO - ✅ 代理关闭操作
2025-08-04 15:28:16,092 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:28:17,061 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:28:17,061 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:28:17,061 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:28:17,062 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:28:17,062 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:28:17,559 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:28:17,560 - INFO - 🔄 Cookie抓取器进程已启动，PID: 17292
2025-08-04 15:28:17,560 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:28:20,561 - INFO - 等待代理服务启动...
2025-08-04 15:28:21,433 - INFO - 代理服务已启动并正常工作
2025-08-04 15:28:21,433 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 17292)
2025-08-04 15:28:21,434 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:28:21,434 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:28:21,435 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:28:21,436 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:28:21,436 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:28:21,437 - INFO - 正在查找微信主窗口...
2025-08-04 15:28:23,450 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:28:23,450 - INFO - 正在激活微信窗口...
2025-08-04 15:28:25,962 - INFO - 微信窗口已激活。
2025-08-04 15:28:25,962 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:28:32,737 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:28:32,737 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:28:36,061 - INFO - 正在查找聊天输入框...
2025-08-04 15:28:38,062 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:28:38,075 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:28:38,076 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:28:39,669 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:28:41,956 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:28:42,123 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:28:42,932 - INFO - 链接已发送。
2025-08-04 15:28:45,933 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:28:48,030 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:28:48,766 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:28:51,767 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:28:51,767 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:28:51,768 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:28:51,781 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:28:54,487 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:28:55,988 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:04,368 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:29:04,368 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:29:04,369 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:29:04,369 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:04,369 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:04,371 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:07,077 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:07,578 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:07,579 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:07,587 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:10,291 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:11,792 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:20,577 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:29:20,578 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:29:23,078 - INFO - 第 1 次刷新完成
2025-08-04 15:29:24,579 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:29:24,580 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:24,581 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:24,589 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:27,294 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:27,795 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:27,796 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:27,807 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:30,514 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:32,016 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:40,725 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:29:40,726 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:29:43,227 - INFO - 第 2 次刷新完成
2025-08-04 15:29:44,728 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:29:44,729 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:44,729 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:44,735 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:47,440 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:47,942 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:47,942 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:47,951 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:50,655 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:52,156 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:30:00,887 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:30:00,888 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:30:03,388 - INFO - 第 3 次刷新完成
2025-08-04 15:30:03,389 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:30:03,390 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:30:03,391 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:30:03,391 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:30:04,392 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:30:04,394 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:30:04,395 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:30:04,395 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:30:04,396 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:30:04,397 - INFO - 正在停止Cookie抓取器 (PID: 17292)...
2025-08-04 15:30:04,400 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:30:04,400 - INFO - 正在验证并清理代理设置...
2025-08-04 15:30:04,401 - INFO - === 开始重置网络状态 ===
2025-08-04 15:30:04,401 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:30:04,527 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:30:04,527 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:30:04,527 - INFO - 系统代理已成功关闭
2025-08-04 15:30:04,527 - INFO - ✅ 代理关闭操作
2025-08-04 15:30:04,527 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:30:06,720 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:30:06,720 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:30:06,720 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:30:09,334 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:30:09,334 - INFO - ✅ 网络连接验证正常
2025-08-04 15:30:12,334 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:30:12,335 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
