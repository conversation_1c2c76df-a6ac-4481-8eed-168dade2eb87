# coding:utf-8
"""
测试SSL证书错误处理功能
"""

import time
from wechat_browser_automation import WeChatBrowserAutomation

def test_ssl_error_handling():
    """测试SSL证书错误处理功能"""
    print("🧪 开始测试SSL证书错误处理功能...")
    
    # 初始化自动化控制器
    automation = WeChatBrowserAutomation()
    
    # 测试1: 独立测试SSL错误检测
    print("\n--- 测试1: 独立SSL错误检测 ---")
    print("💡 请手动打开一个有SSL证书错误的页面，然后按回车继续...")
    input("按回车键继续...")
    
    # 检测SSL错误
    ssl_detected = automation.handle_ssl_certificate_error()
    if ssl_detected:
        print("✅ 成功检测并处理了SSL证书错误")
    else:
        print("ℹ️ 未检测到SSL证书错误页面")
    
    # 测试2: 测试完整的微信文章打开流程（包含SSL处理）
    print("\n--- 测试2: 完整流程测试 ---")
    test_url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect"
    
    confirm = input(f"是否测试完整的微信文章打开流程？(y/N): ").strip().lower()
    if confirm == 'y':
        print("🚀 开始执行完整流程...")
        success = automation.send_and_open_latest_link(
            test_url, 
            auto_refresh=True, 
            refresh_count=2,  # 减少刷新次数用于测试
            refresh_delay=3.0
        )
        
        if success:
            print("✅ 完整流程执行成功！SSL错误处理功能已集成")
        else:
            print("❌ 完整流程执行失败，请检查日志")
    
    print("\n🎉 SSL错误处理功能测试完成！")

def test_browser_window_detection():
    """测试浏览器窗口检测功能"""
    print("\n🔍 测试浏览器窗口检测...")
    
    automation = WeChatBrowserAutomation()
    
    print("💡 请确保有浏览器窗口打开，然后按回车继续...")
    input("按回车键继续...")
    
    # 测试浏览器窗口检测
    found = automation.find_and_activate_browser_window()
    if found:
        print("✅ 成功找到并激活浏览器窗口")
        
        # 测试SSL错误检测
        print("🔍 在当前窗口中检测SSL错误...")
        ssl_detected = automation.handle_ssl_certificate_error()
        if ssl_detected:
            print("✅ 检测到SSL错误并已处理")
        else:
            print("ℹ️ 当前页面无SSL错误")
    else:
        print("❌ 未找到浏览器窗口")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 SSL证书错误处理功能测试工具")
    print("=" * 60)
    
    print("\n选择测试项目:")
    print("1. 测试SSL错误处理功能")
    print("2. 测试浏览器窗口检测")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        test_ssl_error_handling()
    elif choice == "2":
        test_browser_window_detection()
    elif choice == "3":
        print("👋 退出测试")
    else:
        print("❌ 无效选择")
