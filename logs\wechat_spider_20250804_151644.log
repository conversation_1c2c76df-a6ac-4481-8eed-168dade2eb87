2025-08-04 15:16:44,885 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:16:44,885 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:16:44,885 - INFO - ================================================================================
2025-08-04 15:16:44,885 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:16:44,885 - INFO - ================================================================================
2025-08-04 15:16:44,886 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:16:44,963 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:16:44,964 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:16:44,964 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:16:44,964 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:16:44,964 - INFO - ============================================================
2025-08-04 15:16:44,964 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:16:44,964 - INFO - ============================================================
2025-08-04 15:16:44,964 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:16:44,965 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:16:44,965 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:16:44,965 - INFO - === 开始重置网络状态 ===
2025-08-04 15:16:44,965 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:16:45,047 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:16:45,047 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:16:45,047 - INFO - 系统代理已成功关闭
2025-08-04 15:16:45,048 - INFO - ✅ 代理关闭操作
2025-08-04 15:16:45,048 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:16:46,360 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:16:46,361 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:16:46,361 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:16:46,362 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:16:46,363 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:16:46,859 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:16:46,860 - INFO - 🔄 Cookie抓取器进程已启动，PID: 4644
2025-08-04 15:16:46,860 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:16:49,860 - INFO - 等待代理服务启动...
2025-08-04 15:16:50,345 - INFO - 代理服务已启动并正常工作
2025-08-04 15:16:50,345 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 4644)
2025-08-04 15:16:50,346 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:16:50,346 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:16:50,347 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:16:50,348 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:16:50,348 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:16:50,349 - INFO - 正在查找微信主窗口...
2025-08-04 15:16:50,420 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:16:50,421 - INFO - 正在激活微信窗口...
2025-08-04 15:16:52,929 - INFO - 微信窗口已激活。
2025-08-04 15:16:52,930 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:16:59,683 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:16:59,684 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:17:03,017 - INFO - 正在查找聊天输入框...
2025-08-04 15:17:05,017 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:17:05,030 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:17:05,030 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:17:06,606 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:17:08,892 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:17:09,070 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:17:10,067 - INFO - 链接已发送。
2025-08-04 15:17:13,068 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:17:15,161 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:17:15,900 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:17:18,901 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:17:18,902 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:18,902 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:18,910 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:17:21,616 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:17:23,117 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:17:31,318 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:17:31,318 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:17:31,318 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:17:31,318 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:31,318 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:31,328 - INFO - 尝试传统窗口查找方法...
2025-08-04 15:17:35,419 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 15:17:37,233 - INFO - 已成功激活浏览器窗口
2025-08-04 15:17:37,734 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:37,735 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:37,746 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:17:40,452 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:17:41,953 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:17:50,642 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:17:50,643 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:17:53,144 - INFO - 第 1 次刷新完成
2025-08-04 15:17:54,644 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:17:54,645 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:54,646 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:54,667 - INFO - 尝试传统窗口查找方法...
2025-08-04 15:18:01,111 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 15:18:02,923 - INFO - 已成功激活浏览器窗口
2025-08-04 15:18:03,424 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:03,424 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:03,438 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:06,143 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:07,644 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:18:16,389 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:18:16,390 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:18:18,891 - INFO - 第 2 次刷新完成
2025-08-04 15:18:20,392 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:18:20,393 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:20,394 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:20,408 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:23,114 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:23,615 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:23,616 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:23,633 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:26,339 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:27,840 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:18:36,543 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:18:36,544 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:18:39,045 - INFO - 第 3 次刷新完成
2025-08-04 15:18:39,046 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:18:39,048 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:18:39,049 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:18:39,050 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
