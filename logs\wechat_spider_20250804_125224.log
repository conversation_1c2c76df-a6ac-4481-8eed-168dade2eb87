2025-08-04 12:52:24,679 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 12:52:24,679 - INFO - ================================================================================
2025-08-04 12:52:24,680 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 12:52:24,680 - INFO - ================================================================================
2025-08-04 12:52:24,680 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:52:24,750 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:52:24,751 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:52:24,752 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:52:24,752 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 12:52:24,752 - INFO - ============================================================
2025-08-04 12:52:24,752 - INFO - 📍 处理第 1/2 个公众号: 钟山清风
2025-08-04 12:52:24,752 - INFO - ============================================================
2025-08-04 12:52:24,752 - INFO - [步骤 1/5] 为 '钟山清风' 启动 Cookie 抓取器...
2025-08-04 12:52:24,752 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 12:52:24,752 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 12:52:24,752 - INFO - === 开始重置网络状态 ===
2025-08-04 12:52:24,752 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:52:24,856 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:52:24,856 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:52:24,856 - INFO - 系统代理已成功关闭
2025-08-04 12:52:24,857 - INFO - ✅ 代理关闭操作
2025-08-04 12:52:24,857 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:52:25,757 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:52:25,757 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:52:25,758 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 12:52:25,758 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 12:52:25,759 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 12:52:26,266 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 12:52:26,267 - INFO - 🔄 Cookie抓取器进程已启动，PID: 11556
2025-08-04 12:52:26,267 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 12:52:29,268 - INFO - 等待代理服务启动...
2025-08-04 12:52:29,751 - INFO - 代理服务已启动并正常工作
2025-08-04 12:52:29,751 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 11556)
2025-08-04 12:52:29,751 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 12:52:29,752 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-04 12:52:29,752 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:52:29,753 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 12:52:29,753 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 12:52:29,753 - INFO - 正在查找微信主窗口...
2025-08-04 12:52:29,813 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 12:52:29,813 - INFO - 正在激活微信窗口...
2025-08-04 12:52:32,328 - INFO - 微信窗口已激活。
2025-08-04 12:52:32,329 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 12:52:38,948 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 12:52:38,949 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 12:52:42,281 - INFO - 正在查找聊天输入框...
2025-08-04 12:52:44,282 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 12:52:44,289 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 12:52:44,289 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 12:52:45,869 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 12:52:48,158 - INFO - 链接已粘贴，正在发送...
2025-08-04 12:52:48,304 - INFO - 找到发送按钮，点击发送...
2025-08-04 12:52:49,111 - INFO - 链接已发送。
2025-08-04 12:52:52,112 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 12:52:54,202 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 12:52:54,929 - INFO - ✅ 成功点击最新链接。
2025-08-04 12:52:57,930 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 12:52:57,931 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:52:57,932 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:52:57,945 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:00,651 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:02,151 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:09,640 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 12:53:09,640 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 12:53:09,640 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 12:53:09,640 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:09,640 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:09,644 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:12,350 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:12,851 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:12,852 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:12,867 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:15,571 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:17,072 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:24,981 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:53:24,981 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:53:27,482 - INFO - 第 1 次刷新完成
2025-08-04 12:53:28,983 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 12:53:28,983 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:28,984 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:28,994 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:31,699 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:32,200 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:32,201 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:32,213 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:34,921 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:36,423 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:44,376 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:53:44,377 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:53:46,878 - INFO - 第 2 次刷新完成
2025-08-04 12:53:48,379 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 12:53:48,380 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:48,380 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:48,389 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:51,094 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:51,596 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:51,597 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:51,605 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:54,311 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:55,812 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:54:03,731 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:54:03,732 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:54:06,232 - INFO - 第 3 次刷新完成
2025-08-04 12:54:06,233 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 12:54:06,234 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 12:54:06,235 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-04 12:54:06,235 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 12:54:07,236 - INFO - 检测到Cookie文件已生成。
2025-08-04 12:54:07,238 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 12:54:07,239 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 12:54:07,239 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-04 12:54:07,239 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 12:54:07,240 - INFO - 正在停止Cookie抓取器 (PID: 11556)...
2025-08-04 12:54:07,242 - INFO - Cookie抓取器已成功终止。
2025-08-04 12:54:07,243 - INFO - 正在验证并清理代理设置...
2025-08-04 12:54:07,243 - INFO - === 开始重置网络状态 ===
2025-08-04 12:54:07,243 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:54:07,392 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:54:07,392 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:54:07,393 - INFO - 系统代理已成功关闭
2025-08-04 12:54:07,393 - INFO - ✅ 代理关闭操作
2025-08-04 12:54:07,393 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:54:09,375 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:54:09,375 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:54:09,376 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 12:54:10,707 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:54:10,708 - INFO - ✅ 网络连接验证正常
2025-08-04 12:54:13,709 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 12:54:13,710 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-04 12:57:20,114 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 18 篇文章
2025-08-04 12:57:20,114 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250804_125720.xlsx
2025-08-04 12:57:20,114 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 12:57:35,114 - INFO - ============================================================
2025-08-04 12:57:35,115 - INFO - 📍 处理第 2/2 个公众号: 金陵档案
2025-08-04 12:57:35,115 - INFO - ============================================================
2025-08-04 12:57:35,116 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 12:57:35,116 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 12:57:35,116 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 12:57:35,116 - INFO - === 开始重置网络状态 ===
2025-08-04 12:57:35,116 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:57:35,204 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:57:35,204 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:57:35,205 - INFO - 系统代理已成功关闭
2025-08-04 12:57:35,205 - INFO - ✅ 代理关闭操作
2025-08-04 12:57:35,205 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:57:37,878 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:57:37,879 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:57:37,879 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 12:57:37,880 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 12:57:37,881 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 12:57:38,420 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 12:57:38,421 - INFO - 🔄 Cookie抓取器进程已启动，PID: 37452
2025-08-04 12:57:38,421 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 12:57:41,421 - INFO - 等待代理服务启动...
2025-08-04 12:57:43,235 - INFO - 代理服务已启动并正常工作
2025-08-04 12:57:43,235 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 37452)
2025-08-04 12:57:43,235 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 12:57:43,235 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 12:57:43,235 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:57:43,236 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 12:57:43,236 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 12:57:43,236 - INFO - 正在查找微信主窗口...
2025-08-04 12:57:43,477 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 12:57:43,477 - INFO - 正在激活微信窗口...
2025-08-04 12:57:45,983 - INFO - 微信窗口已激活。
2025-08-04 12:57:45,984 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 12:57:52,696 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 12:57:52,696 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 12:57:56,031 - INFO - 正在查找聊天输入框...
2025-08-04 12:57:58,032 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 12:57:58,037 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 12:57:58,037 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 12:57:59,607 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&mid=2247539053&idx=1&sn=9dcae8e371bf87efbe121cf3ed8afee7&chksm=cf1b71bdf86cf8abd355f4b297ae51941771cc98fc1eb481b875e64b6bee30ac39c4f06a4133#rd
2025-08-04 12:58:01,896 - INFO - 链接已粘贴，正在发送...
2025-08-04 12:58:02,030 - INFO - 找到发送按钮，点击发送...
2025-08-04 12:58:02,845 - INFO - 链接已发送。
2025-08-04 12:58:05,847 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 12:58:07,935 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 12:58:08,659 - INFO - ✅ 成功点击最新链接。
2025-08-04 12:58:11,660 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 12:58:11,661 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:11,663 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:11,671 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:14,377 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:15,879 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:23,257 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 12:58:23,258 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 12:58:23,258 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 12:58:23,258 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:23,258 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:23,261 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:25,968 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:26,469 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:26,470 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:26,483 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:29,193 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:30,693 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:38,594 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:58:38,595 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:58:41,096 - INFO - 第 1 次刷新完成
2025-08-04 12:58:42,597 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 12:58:42,598 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:42,598 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:42,606 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:45,312 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:45,814 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:45,815 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:45,826 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:48,532 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:50,033 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:57,999 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:58:57,999 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:59:00,500 - INFO - 第 2 次刷新完成
2025-08-04 12:59:02,001 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 12:59:02,001 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:59:02,001 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:59:02,005 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:59:04,712 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:59:05,213 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:59:05,213 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:59:05,225 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:59:07,932 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:59:09,433 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:59:17,341 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:59:17,342 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:59:19,843 - INFO - 第 3 次刷新完成
2025-08-04 12:59:19,843 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 12:59:19,844 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 12:59:19,844 - INFO - [步骤 3/5] 等待 '金陵档案' 的 Cookie 数据...
2025-08-04 12:59:19,845 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 12:59:20,846 - INFO - 检测到Cookie文件已生成。
2025-08-04 12:59:20,847 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 12:59:20,847 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 12:59:20,848 - INFO - [步骤 4/5] 停止 '金陵档案' 的 Cookie 抓取器...
2025-08-04 12:59:20,848 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 12:59:20,848 - INFO - 正在停止Cookie抓取器 (PID: 37452)...
2025-08-04 12:59:20,850 - INFO - Cookie抓取器已成功终止。
2025-08-04 12:59:20,850 - INFO - 正在验证并清理代理设置...
2025-08-04 12:59:20,850 - INFO - === 开始重置网络状态 ===
2025-08-04 12:59:20,851 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:59:20,954 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:59:20,954 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:59:20,954 - INFO - 系统代理已成功关闭
2025-08-04 12:59:20,954 - INFO - ✅ 代理关闭操作
2025-08-04 12:59:20,954 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:59:22,476 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:59:22,476 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:59:22,476 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 12:59:23,463 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:59:23,464 - INFO - ✅ 网络连接验证正常
2025-08-04 12:59:26,464 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 12:59:26,465 - INFO - [步骤 5/5] 开始爬取 '金陵档案' 的文章...
2025-08-04 12:59:26,611 - WARNING - ⚠️ 公众号 '金陵档案' 未获取到任何文章数据
2025-08-04 12:59:26,611 - INFO - ================================================================================
2025-08-04 12:59:26,611 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 12:59:26,612 - INFO - ================================================================================
2025-08-04 12:59:26,612 - INFO - ✅ 成功处理: 1 个公众号
2025-08-04 12:59:26,612 - INFO - ❌ 失败处理: 1 个公众号
2025-08-04 12:59:26,612 - INFO - 📄 总计文章: 18 篇
2025-08-04 12:59:26,643 - INFO - 🎉 汇总数据已保存到:
2025-08-04 12:59:26,644 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_125926.xlsx
2025-08-04 12:59:26,644 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_125926.json
2025-08-04 12:59:26,644 - INFO - ================================================================================
2025-08-04 12:59:26,644 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 12:59:26,644 - INFO - ================================================================================
