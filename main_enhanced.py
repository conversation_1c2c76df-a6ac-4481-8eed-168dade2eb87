# coding:utf-8
# main_enhanced.py
"""
微信公众号爬虫工具集 v3.0 - 全自动化版本
专为Windows任务计划程序设计，无需任何用户交互，直接执行Excel全自动爬取流程。
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider
from excel_auto_crawler import ExcelAutoCrawler
from automated_crawler import AutomatedCrawler # 导入新的自动化控制器

# 配置日志系统
def setup_logging():
    """设置日志系统，同时输出到控制台和文件"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_filename = os.path.join(log_dir, f"wechat_spider_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def auto_excel_crawler():
    """全自动Excel爬取流程 - 无需用户交互"""
    logger = logging.getLogger()

    logger.info("="*80)
    logger.info("🚀 微信公众号全自动爬取流程启动 🚀")
    logger.info("="*80)
    logger.info("版本: v3.0 - 全自动化版本")
    logger.info("设计用途: Windows任务计划程序自动执行")
    logger.info("执行时间: %s", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    logger.info("="*80)

    # 检查Excel文件是否存在
    excel_file = "target_articles.xlsx"
    if not os.path.exists(excel_file):
        logger.error("❌ 未找到Excel文件: %s", excel_file)
        logger.error("请确保在项目根目录下存在包含公众号信息的Excel文件。")
        return False

    try:
        # 初始化并运行爬虫
        logger.info("正在初始化Excel自动化爬取器...")
        crawler = ExcelAutoCrawler(excel_file)

        logger.info("开始执行全自动爬取流程...")
        crawler.auto_crawl_from_excel()

        logger.info("="*80)
        logger.info("✅ Excel全自动爬取流程执行完毕")
        logger.info("详细结果请查看上方的日志输出")
        logger.info("="*80)
        return True

    except ImportError as e:
        logger.error("❌ 关键依赖库缺失: %s", str(e))
        logger.error("请确保已安装所有必要的依赖库:")
        logger.error("  pip install -r requirements.txt")
        return False

    except Exception as e:
        logger.error("❌ 自动化爬取过程中发生未知错误: %s", str(e))
        logger.error("详细错误信息:")
        logger.error(traceback.format_exc())
        return False

def show_menu():
    """显示主菜单 - 保留用于手动调试"""
    print("\n" + "="*60)
    print("      🚀 微信公众号爬虫工具集 v3.0 🚀")
    print("="*60)
    print("1. 抓取Cookie (手动模式)")
    print("2. 批量爬取文章 (使用已有Cookie)")
    print("3. 【推荐】从Excel启动全自动爬取")
    print("4. 全自动模式 (无交互)")
    print("5. 退出程序")
    print("="*60)
    print("💡 v3.0 更新: 新增全自动模式，适配Windows任务计划程序。")

def extract_cookies():
    """手动抓取Cookie"""
    print("\n🔧 启动手动Cookie抓取器...")
    cookie_reader = ReadCookie()

    # 启动抓取器
    if cookie_reader.start_cookie_extractor(timeout=10):
        print("\n抓取器已启动。请手动在微信中打开任意公众号文章并刷新几次。")
        print("等待抓取完成...")
    else:
        print("抓取器启动失败，请检查mitmproxy是否安装。")
        return

    # 解析cookie
    if cookie_reader.wait_for_new_cookie(timeout=120):
        result = cookie_reader.get_latest_cookies()
        print("\n" + "="*50)
        print("✅ Cookie解析成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   解析时间: {result['timestamp']}")
        print("="*50)
    else:
        print("❌ Cookie抓取超时或失败。")

def batch_readnum_crawler():
    """批量文章内容+统计数据抓取"""
    print("\n📊 启动批量爬取器 (使用已有Cookie)...")

    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到 wechat_keys.txt 文件。请先运行选项1或3抓取Cookie。")
        return

    try:
        max_pages = int(input("最大页数 (默认3): ") or "3")
        days_back = int(input("抓取多少天内的文章 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数输入无效，使用默认值。")
        max_pages, days_back = 3, 7

    spider = BatchReadnumSpider()
    try:
        print(f"\n🚀 开始批量抓取: {max_pages}页, {days_back}天内文章。")
        spider.batch_crawl_readnum(max_pages=max_pages, days_back=days_back)

        if spider.articles_data:
            spider.print_summary()
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            print(f"\n🎉 抓取完成！结果已保存到 {excel_file} 和 {json_file}")
        else:
            print("❌ 未获取到任何数据，请检查Cookie是否有效或公众号近期有无发文。")

    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        traceback.print_exc()

def excel_auto_crawler_entry():
    """从Excel启动全自动爬取流程的入口 - 交互式版本"""
    print("\n" + "="*60)
    print("🤖 启动Excel全自动爬取流程...")
    print("="*60)
    print("💡 此功能将: ")
    print("   1. 从 `target_articles.xlsx` 读取目标公众号列表。")
    print("   2. 自动操作微信，打开文章链接以获取最新的Cookie。")
    print("   3. 使用获取的Cookie，批量爬取列表中所有公众号的文章。")

    confirm = input("是否确认开始？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消。")
        return

    try:
        # 初始化并运行爬虫
        crawler = ExcelAutoCrawler()
        crawler.auto_crawl_from_excel()
        print("\n✅ Excel自动化爬取流程执行完毕。请查看上方的日志输出获取详细信息。")

    except ImportError:
        print("\n❌ 关键库 `uiautomation` 未安装，无法执行此功能。")
        print("💡 请先运行 `pip install uiautomation` 和 `pip install pyperclip`。")
    except Exception as e:
        print(f"\n❌ 自动化爬取过程中发生未知错误: {e}")
        traceback.print_exc()

def main():
    """主程序入口"""
    # 检查命令行参数，如果包含 --auto 或没有参数，则执行新的全自动化流程
    if '--auto' in sys.argv or len(sys.argv) == 1:
        logger = setup_logging()
        logger.info("检测到 --auto 参数或无参数，启动全新自动化爬取流程...")
        try:
            crawler = AutomatedCrawler()
            success = crawler.run()
            sys.exit(0 if success else 1)
        except Exception as e:
            logger.error(f"自动化流程启动失败: {e}")
            logger.error(traceback.format_exc())
            sys.exit(1)

    # 如果有 --interactive 参数，则显示交互式菜单
    elif '--interactive' in sys.argv:
        # 交互模式不需要文件日志，直接输出到控制台即可
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        while True:
            show_menu()
            choice = input("\n请选择功能 (1-5): ").strip()
            if choice == '1':
                extract_cookies()
            elif choice == '2':
                batch_readnum_crawler()
            elif choice == '3':
                excel_auto_crawler_entry()
            elif choice == '4':
                # 在交互模式下选择4，也运行新的自动化流程
                logger = setup_logging()
                logger.info("从菜单启动全新自动化爬取流程...")
                crawler = AutomatedCrawler()
                crawler.run()
            elif choice == '5':
                print("👋 感谢使用，再见！")
                break
            else:
                print("❌ 无效选择，请输入1-5。")
    else:
        print("无效的命令行参数。请使用 --auto, --interactive, 或不带参数运行。")
        sys.exit(1)

if __name__ == '__main__':
    main()