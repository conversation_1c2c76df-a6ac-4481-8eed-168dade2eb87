2025-08-04 15:01:08,093 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:01:08,093 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:08,093 - INFO - ================================================================================
2025-08-04 15:01:08,093 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:01:08,093 - INFO - ================================================================================
2025-08-04 15:01:08,093 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:01:08,730 - INFO - 找到有效目标 1: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 15:01:08,730 - INFO - 找到有效目标 2: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 15:01:08,730 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:01:08,730 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:01:08,730 - INFO - ============================================================
2025-08-04 15:01:08,731 - INFO - 📍 处理第 1/2 个公众号: 金陵档案
2025-08-04 15:01:08,731 - INFO - ============================================================
2025-08-04 15:01:08,731 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 15:01:08,731 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:01:08,731 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:01:08,731 - INFO - === 开始重置网络状态 ===
2025-08-04 15:01:08,731 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:01:08,857 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:01:08,857 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:01:08,858 - INFO - 系统代理已成功关闭
2025-08-04 15:01:08,858 - INFO - ✅ 代理关闭操作
2025-08-04 15:01:08,858 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:01:10,375 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:01:10,376 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:01:10,376 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:01:10,376 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:01:10,377 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:01:12,859 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:01:12,860 - INFO - 🔄 Cookie抓取器进程已启动，PID: 12964
2025-08-04 15:01:12,860 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:01:15,860 - INFO - 等待代理服务启动...
2025-08-04 15:01:16,320 - INFO - 代理服务已启动并正常工作
2025-08-04 15:01:16,320 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 12964)
2025-08-04 15:01:16,320 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:01:16,320 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 15:01:16,320 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:16,321 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:01:16,321 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:01:16,321 - INFO - 正在查找微信主窗口...
2025-08-04 15:01:16,404 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:01:16,405 - INFO - 正在激活微信窗口...
2025-08-04 15:01:18,920 - INFO - 微信窗口已激活。
2025-08-04 15:01:18,921 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:01:25,931 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:01:25,932 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:01:29,264 - INFO - 正在查找聊天输入框...
