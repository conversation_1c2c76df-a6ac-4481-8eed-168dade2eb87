#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的cookie_extractor.py
验证代理设置时序和自动清理功能
"""

import os
import time
import subprocess
import winreg
from read_cookie import ReadCookie

def check_proxy_status():
    """检查当前系统代理状态"""
    try:
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                           0, winreg.KEY_READ)
        proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
        try:
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
        except:
            proxy_server = ""
        winreg.CloseKey(key)
        
        status = "启用" if proxy_enable == 1 else "禁用"
        print(f"📊 当前代理状态: {status}")
        if proxy_enable == 1:
            print(f"📊 代理服务器: {proxy_server}")
        return proxy_enable == 1, proxy_server
    except Exception as e:
        print(f"❌ 检查代理状态失败: {e}")
        return False, ""

def test_improved_workflow():
    """测试改进后的工作流程"""
    print("🧪 测试改进后的Cookie抓取器工作流程")
    print("="*60)
    
    # 1. 检查初始代理状态
    print("\n1️⃣ 检查初始代理状态:")
    initial_enabled, initial_server = check_proxy_status()
    
    # 2. 启动改进后的Cookie抓取器
    print("\n2️⃣ 启动改进后的Cookie抓取器:")
    cookie_reader = ReadCookie()
    
    if not cookie_reader.start_cookie_extractor(timeout=10):
        print("❌ Cookie抓取器启动失败")
        return False
    
    print("✅ Cookie抓取器已启动")
    
    # 3. 监控代理状态变化
    print("\n3️⃣ 监控代理状态变化:")
    print("等待代理自动设置...")
    
    # 等待最多40秒观察代理状态变化
    for i in range(40):
        enabled, server = check_proxy_status()
        if enabled and "127.0.0.1:8080" in server:
            print(f"✅ 代理已自动设置 (等待了 {i+1} 秒)")
            break
        time.sleep(1)
    else:
        print("⚠️ 代理设置超时，但继续测试...")
    
    # 4. 提示用户操作
    print("\n4️⃣ 请手动操作:")
    print("请在微信中打开任意公众号文章并刷新页面")
    print("等待Cookie被抓取...")
    print("按 Enter 键继续监控，或等待60秒自动继续...")
    
    # 等待用户输入或超时
    import select
    import sys
    
    start_time = time.time()
    while time.time() - start_time < 60:
        # 检查是否有新的Cookie数据
        if cookie_reader.wait_for_new_cookie(timeout=5):
            print("🎉 检测到新的Cookie数据！")
            break
        
        # 检查代理状态
        enabled, server = check_proxy_status()
        if not enabled:
            print("🎯 检测到代理已自动关闭！")
            break
    
    # 5. 检查最终状态
    print("\n5️⃣ 检查最终状态:")
    final_enabled, final_server = check_proxy_status()
    
    # 6. 清理
    print("\n6️⃣ 清理资源:")
    cookie_reader.stop_cookie_extractor()
    
    # 7. 总结
    print("\n📋 测试总结:")
    print(f"初始代理状态: {'启用' if initial_enabled else '禁用'}")
    print(f"最终代理状态: {'启用' if final_enabled else '禁用'}")
    
    if not initial_enabled and not final_enabled:
        print("✅ 测试成功：代理正确地被设置和清理")
        return True
    else:
        print("⚠️ 测试结果需要人工确认")
        return False

def main():
    """主函数"""
    try:
        success = test_improved_workflow()
        if success:
            print("\n🎉 改进后的Cookie抓取器工作正常！")
        else:
            print("\n⚠️ 测试完成，请检查结果")
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
