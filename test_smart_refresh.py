# coding:utf-8
# test_smart_refresh.py
"""
测试智能刷新功能 - 当成功抓包后自动停止刷新
"""

import logging
import time
from wechat_browser_automation import WeChatBrowserAutomation, UI_AUTOMATION_AVAILABLE
from read_cookie import ReadCookie

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_refresh_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_smart_refresh():
    """测试智能刷新功能"""
    print("="*80)
    print("🧪 智能刷新功能测试")
    print("="*80)
    print("💡 此测试将验证以下功能:")
    print("   1. 启动mitmproxy抓包器")
    print("   2. 自动打开微信文章链接")
    print("   3. 在刷新过程中检测抓包状态")
    print("   4. 一旦抓包成功，立即停止刷新")
    print("   5. 避免不必要的频繁刷新")
    print()
    
    if not UI_AUTOMATION_AVAILABLE:
        print("❌ UI自动化库不可用，无法执行测试")
        return False
    
    # 测试用的微信文章链接 - 请替换为有效链接
    test_url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect"
    
    print(f"🔗 测试链接: {test_url[:60]}...")
    print()
    
    confirm = input("是否开始测试？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return False
    
    try:
        # 初始化组件
        print("\n📋 步骤 1: 初始化组件...")
        automation = WeChatBrowserAutomation()
        cookie_reader = ReadCookie(outfile="smart_refresh_test_keys.txt")
        
        # 启动抓包器
        print("\n📋 步骤 2: 启动mitmproxy抓包器...")
        if not cookie_reader.start_cookie_extractor():
            print("❌ 抓包器启动失败")
            return False
        print("✅ 抓包器已启动")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行智能刷新测试
        print("\n📋 步骤 3: 执行智能刷新测试...")
        print("🔍 将监控抓包状态，一旦成功抓包就停止刷新")
        
        success = automation.send_and_open_latest_link(
            test_url,
            auto_refresh=True,
            refresh_count=10,  # 设置较多次数，测试是否会提前停止
            refresh_delay=3.0,  # 稍长的延迟，便于观察
            cookie_reader=cookie_reader  # 传递cookie_reader启用智能停止
        )
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n📋 步骤 4: 测试结果分析...")
        print(f"⏱️ 总耗时: {total_time:.1f} 秒")
        
        if success:
            print("✅ UI自动化操作成功")
            
            # 检查是否成功抓包
            if cookie_reader.wait_for_new_cookie(timeout=5):
                cookie_data = cookie_reader.get_latest_cookies()
                if cookie_data:
                    print("🎉 智能刷新功能测试成功！")
                    print(f"   ✅ 成功抓取到Cookie")
                    print(f"   ✅ __biz: {cookie_data['biz']}")
                    print(f"   ✅ appmsg_token: {cookie_data['appmsg_token'][:20]}...")
                    print(f"   ✅ 抓取时间: {cookie_data['timestamp']}")
                    
                    if total_time < 30:  # 如果总时间少于30秒，说明提前停止了
                        print("   🚀 刷新提前停止，避免了不必要的频繁刷新！")
                    else:
                        print("   ⚠️ 刷新可能没有提前停止，请检查日志")
                else:
                    print("⚠️ Cookie数据解析失败")
            else:
                print("⚠️ 未检测到新的Cookie数据")
        else:
            print("❌ UI自动化操作失败")
        
        # 停止抓包器
        print("\n📋 步骤 5: 清理资源...")
        cookie_reader.stop_cookie_extractor()
        print("✅ 抓包器已停止")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logging.exception("测试异常")
        return False

def test_traditional_refresh():
    """测试传统刷新功能（不启用智能停止）"""
    print("\n" + "="*80)
    print("🔄 传统刷新功能测试（对比组）")
    print("="*80)
    print("💡 此测试将使用传统刷新模式（不检测抓包状态）")
    print()
    
    if not UI_AUTOMATION_AVAILABLE:
        print("❌ UI自动化库不可用，无法执行测试")
        return False
    
    test_url = "https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect"
    
    confirm = input("是否测试传统刷新模式？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return False
    
    try:
        automation = WeChatBrowserAutomation()
        
        start_time = time.time()
        
        # 不传递cookie_reader，使用传统模式
        success = automation.send_and_open_latest_link(
            test_url,
            auto_refresh=True,
            refresh_count=3,  # 较少次数用于对比
            refresh_delay=3.0
            # 注意：没有传递cookie_reader参数
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n📊 传统模式结果:")
        print(f"⏱️ 总耗时: {total_time:.1f} 秒")
        print(f"🔄 执行了完整的 3 次刷新")
        
        return success
        
    except Exception as e:
        print(f"❌ 传统模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 智能刷新功能完整测试套件")
    print("="*80)
    
    # 测试1: 智能刷新
    result1 = test_smart_refresh()
    
    # 等待一段时间
    if result1:
        time.sleep(5)
        
        # 测试2: 传统刷新（对比）
        result2 = test_traditional_refresh()
        
        print("\n" + "="*80)
        print("📊 测试总结")
        print("="*80)
        print(f"智能刷新测试: {'✅ 成功' if result1 else '❌ 失败'}")
        print(f"传统刷新测试: {'✅ 成功' if result2 else '❌ 失败'}")
        print()
        print("💡 预期效果:")
        print("   - 智能刷新应该在抓包成功后立即停止")
        print("   - 传统刷新会执行完所有预设的刷新次数")
        print("   - 智能刷新的总耗时应该更短")

if __name__ == "__main__":
    main()
