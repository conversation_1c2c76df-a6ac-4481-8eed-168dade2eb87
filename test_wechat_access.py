#!/usr/bin/env python3
"""
测试微信公众号访问
验证代理配置是否正确，能否正常访问微信公众号文章
"""

import requests
import time
import subprocess
import logging
from wechat_browser_automation import WeChatBrowserAutomation

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_direct_access():
    """测试直接访问微信公众号"""
    logger = setup_logging()
    
    print("🌐 测试直接访问微信公众号...")
    
    test_urls = [
        "https://mp.weixin.qq.com",
        "https://mp.weixin.qq.com/mp/profile_ext?action=home"
    ]
    
    for url in test_urls:
        try:
            print(f"测试: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {url} 访问成功")
                return True
            else:
                print(f"⚠️ {url} 返回状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {url} 访问失败: {e}")
    
    return False

def test_proxy_access():
    """测试通过代理访问微信公众号"""
    logger = setup_logging()
    
    print("🔄 测试通过代理访问微信公众号...")
    
    proxies = {
        'http': 'http://127.0.0.1:8080',
        'https': 'http://127.0.0.1:8080'
    }
    
    test_urls = [
        "https://mp.weixin.qq.com",
        "http://httpbin.org/ip"  # 用于验证代理是否工作
    ]
    
    for url in test_urls:
        try:
            print(f"通过代理测试: {url}")
            response = requests.get(
                url, 
                proxies=proxies, 
                timeout=10,
                verify=False  # 忽略SSL验证
            )
            
            if response.status_code == 200:
                print(f"✅ {url} 代理访问成功")
                if "httpbin.org" in url:
                    # 显示代理IP信息
                    try:
                        data = response.json()
                        print(f"   代理IP: {data.get('origin', '未知')}")
                    except:
                        pass
                return True
            else:
                print(f"⚠️ {url} 返回状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {url} 代理访问失败: {e}")
    
    return False

def test_wechat_automation():
    """测试微信自动化功能"""
    print("🤖 测试微信自动化功能...")

    try:
        # 检查微信进程是否运行
        result = subprocess.run(
            ['tasklist', '/fi', 'imagename eq WeChat.exe'],
            capture_output=True,
            text=True,
            timeout=5
        )

        if 'WeChat.exe' in result.stdout:
            print("✅ 微信进程正在运行")
            return True
        else:
            print("❌ 微信进程未运行，请启动微信")
            return False

    except Exception as e:
        print(f"❌ 微信自动化测试失败: {e}")
        return False

def check_mitmproxy_status():
    """检查mitmproxy状态"""
    print("🔍 检查mitmproxy状态...")
    
    try:
        # 检查进程
        result = subprocess.run(
            ['tasklist', '/fi', 'imagename eq mitmdump.exe'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if 'mitmdump.exe' in result.stdout:
            print("✅ mitmproxy进程正在运行")
            
            # 检查端口
            port_result = subprocess.run(
                ['netstat', '-an'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if ':8080' in port_result.stdout:
                print("✅ 端口8080已被监听")
                return True
            else:
                print("❌ 端口8080未被监听")
        else:
            print("❌ mitmproxy进程未运行")
            
    except Exception as e:
        print(f"❌ 检查mitmproxy状态失败: {e}")
    
    return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 如果测试失败，请尝试以下解决方案:")
    print("=" * 60)
    print("1. 确保mitmproxy正在运行:")
    print("   python wechat_browser_proxy_config.py")
    print()
    print("2. 重启微信客户端:")
    print("   - 完全退出微信")
    print("   - 等待10秒后重新启动")
    print()
    print("3. 检查防火墙设置:")
    print("   - 允许mitmdump.exe通过防火墙")
    print("   - 或临时关闭防火墙测试")
    print()
    print("4. 清除浏览器缓存:")
    print("   - 清除微信内置浏览器缓存")
    print("   - 重启微信")
    print()
    print("5. 尝试手动访问:")
    print("   - 在微信中手动打开一个公众号文章")
    print("   - 观察是否出现代理错误")
    print()
    print("6. 检查证书安装:")
    print("   - 运行 certmgr.msc")
    print("   - 确认mitmproxy证书在'受信任的根证书颁发机构'中")

def main():
    """主测试函数"""
    print("🧪 微信公众号访问测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查mitmproxy状态
    print("步骤 1/4: 检查mitmproxy状态")
    mitmproxy_ok = check_mitmproxy_status()
    test_results.append(("mitmproxy状态", mitmproxy_ok))
    print()
    
    # 2. 测试直接访问
    print("步骤 2/4: 测试直接访问")
    direct_ok = test_direct_access()
    test_results.append(("直接访问", direct_ok))
    print()
    
    # 3. 测试代理访问
    print("步骤 3/4: 测试代理访问")
    proxy_ok = test_proxy_access()
    test_results.append(("代理访问", proxy_ok))
    print()
    
    # 4. 测试微信自动化
    print("步骤 4/4: 测试微信自动化")
    automation_ok = test_wechat_automation()
    test_results.append(("微信自动化", automation_ok))
    print()
    
    # 输出测试结果
    print("📊 测试结果汇总:")
    print("=" * 60)
    all_passed = True
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！微信公众号访问配置正确。")
        print("现在可以正常运行微信爬虫程序了。")
    else:
        print("⚠️ 部分测试失败，需要进一步排查问题。")
        provide_solutions()

if __name__ == '__main__':
    main()
